#!/bin/bash

echo "🔍 Firecrawl Search Configuration Checker"
echo "========================================="
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check if .env exists
if [ ! -f ".env" ]; then
    echo -e "${RED}❌ .env file not found!${NC}"
    echo "Please create a .env file in the root directory."
    exit 1
fi

echo "📋 Current Search Provider Configuration:"
echo "========================================"

# Function to check and display env var
check_env_var() {
    local var_name=$1
    local description=$2
    
    if grep -q "^${var_name}=" .env; then
        local value=$(grep "^${var_name}=" .env | cut -d'=' -f2-)
        if [ -n "$value" ] && [ "$value" != "" ]; then
            echo -e "${GREEN}✓${NC} $description: ${BLUE}SET${NC}"
            # Show partial value for verification (mask sensitive parts)
            if [ ${#value} -gt 10 ]; then
                echo "  Value: ${value:0:6}...${value: -4}"
            else
                echo "  Value: ${value:0:3}..."
            fi
            return 0
        else
            echo -e "${YELLOW}⚠${NC} $description: ${YELLOW}EMPTY${NC}"
            return 1
        fi
    else
        echo -e "${RED}✗${NC} $description: ${RED}NOT SET${NC}"
        return 1
    fi
}

echo ""
echo "1. 🔍 Search Provider APIs:"
echo "=========================="

PROVIDERS_CONFIGURED=0

# Check Fire Engine (highest priority)
if check_env_var "FIRE_ENGINE_BETA_URL" "Fire Engine Beta URL"; then
    PROVIDERS_CONFIGURED=$((PROVIDERS_CONFIGURED + 1))
    echo "  Priority: 1 (Highest)"
fi

echo ""

# Check Serper
if check_env_var "SERPER_API_KEY" "Serper API Key"; then
    PROVIDERS_CONFIGURED=$((PROVIDERS_CONFIGURED + 1))
    echo "  Priority: 2"
fi

echo ""

# Check Google Custom Search (needs both API key and CSE ID)
GOOGLE_API_SET=0
GOOGLE_CSE_SET=0

if check_env_var "GOOGLE_API_KEY" "Google API Key"; then
    GOOGLE_API_SET=1
fi

echo ""

if check_env_var "GOOGLE_CSE_ID" "Google Custom Search Engine ID"; then
    GOOGLE_CSE_SET=1
fi

if [ $GOOGLE_API_SET -eq 1 ] && [ $GOOGLE_CSE_SET -eq 1 ]; then
    PROVIDERS_CONFIGURED=$((PROVIDERS_CONFIGURED + 1))
    echo -e "${GREEN}✓${NC} Google Custom Search: ${GREEN}FULLY CONFIGURED${NC}"
    echo "  Priority: 3"
elif [ $GOOGLE_API_SET -eq 1 ] || [ $GOOGLE_CSE_SET -eq 1 ]; then
    echo -e "${YELLOW}⚠${NC} Google Custom Search: ${YELLOW}PARTIALLY CONFIGURED${NC}"
    echo "  Both GOOGLE_API_KEY and GOOGLE_CSE_ID are required"
fi

echo ""

# Check SearchAPI
if check_env_var "SEARCHAPI_API_KEY" "SearchAPI Key"; then
    PROVIDERS_CONFIGURED=$((PROVIDERS_CONFIGURED + 1))
    echo "  Priority: 4"
    
    # Check SearchAPI engine
    if check_env_var "SEARCHAPI_ENGINE" "SearchAPI Engine"; then
        echo "  Engine configured"
    else
        echo -e "${BLUE}ℹ${NC} SearchAPI Engine: Using default (google)"
    fi
fi

echo ""

# Check SearXNG
if check_env_var "SEARXNG_ENDPOINT" "SearXNG Endpoint"; then
    PROVIDERS_CONFIGURED=$((PROVIDERS_CONFIGURED + 1))
    echo "  Priority: 5"
fi

echo ""
echo "2. 📊 Configuration Summary:"
echo "==========================="

echo ""
echo "Search Providers Configured: $PROVIDERS_CONFIGURED"

if [ $PROVIDERS_CONFIGURED -eq 0 ]; then
    echo -e "${RED}❌ NO SEARCH PROVIDERS CONFIGURED!${NC}"
    echo ""
    echo "This explains why your search returns empty results."
    echo "The system will fall back to browser-based Google search,"
    echo "which is often blocked by Google's anti-bot measures."
    echo ""
    echo -e "${YELLOW}🔧 IMMEDIATE FIX NEEDED:${NC}"
    echo "Configure at least one search provider (see recommendations below)"
    
elif [ $PROVIDERS_CONFIGURED -eq 1 ]; then
    echo -e "${YELLOW}⚠${NC} Only 1 provider configured - consider adding a backup"
    
else
    echo -e "${GREEN}✓${NC} Multiple providers configured - good redundancy!"
fi

echo ""
echo "3. 🛠️ Quick Setup Recommendations:"
echo "=================================="

echo ""
echo -e "${BLUE}Option 1: SearchAPI (Recommended - Free tier available)${NC}"
echo "1. Visit: https://searchapi.com/"
echo "2. Sign up for free account"
echo "3. Get your API key"
echo "4. Add to .env file:"
echo "   SEARCHAPI_API_KEY=your_api_key_here"
echo "   SEARCHAPI_ENGINE=google"

echo ""
echo -e "${BLUE}Option 2: Serper (Good alternative)${NC}"
echo "1. Visit: https://serper.dev/"
echo "2. Sign up for account"
echo "3. Get your API key"
echo "4. Add to .env file:"
echo "   SERPER_API_KEY=your_api_key_here"

echo ""
echo -e "${BLUE}Option 3: Complete Google Custom Search${NC}"
if [ $GOOGLE_API_SET -eq 1 ]; then
    echo "✓ You already have GOOGLE_API_KEY"
    echo "Missing: GOOGLE_CSE_ID"
    echo "1. Visit: https://cse.google.com/"
    echo "2. Create a Custom Search Engine"
    echo "3. Get your Search Engine ID"
    echo "4. Add to .env file:"
    echo "   GOOGLE_CSE_ID=your_cse_id_here"
else
    echo "1. Get Google API key from: https://console.developers.google.com/"
    echo "2. Enable Custom Search API"
    echo "3. Create Custom Search Engine at: https://cse.google.com/"
    echo "4. Add to .env file:"
    echo "   GOOGLE_API_KEY=your_api_key_here"
    echo "   GOOGLE_CSE_ID=your_cse_id_here"
fi

echo ""
echo "4. 🔄 After Configuration:"
echo "========================="

echo ""
echo "1. Save your .env file"
echo "2. Restart your containers:"
echo "   docker compose down"
echo "   docker compose up -d"
echo ""
echo "3. Test the search:"
echo '   curl -k -X POST "https://web.w-post.com/v1/search" \'
echo '     -H "Content-Type: application/json" \'
echo '     -H "Authorization: Bearer 12345" \'
echo '     -d '"'"'{"query": "test", "limit": 3}'"'"

echo ""
echo "5. 🐛 Current Issue Diagnosis:"
echo "============================="

if [ $PROVIDERS_CONFIGURED -eq 0 ]; then
    echo ""
    echo -e "${RED}ROOT CAUSE IDENTIFIED:${NC}"
    echo "Your search returns empty results because:"
    echo ""
    echo "1. ❌ No API-based search providers are configured"
    echo "2. ❌ System falls back to browser-based Google search"
    echo "3. ❌ Google blocks automated browser requests"
    echo "4. ❌ Result: Empty search results"
    echo ""
    echo -e "${GREEN}SOLUTION:${NC} Configure any one of the search providers above"
    
else
    echo ""
    echo -e "${YELLOW}POSSIBLE ISSUES:${NC}"
    echo "Even with providers configured, you might have empty results due to:"
    echo ""
    echo "1. 🔑 Invalid API keys"
    echo "2. 🚫 Rate limiting"
    echo "3. 🌐 Network connectivity issues"
    echo "4. 🔄 Containers not restarted after configuration"
    echo ""
    echo "Run the diagnostic script to check these issues:"
    echo "./diagnostic_check.sh"
fi

echo ""
echo "6. 📝 Environment File Template:"
echo "==============================="

echo ""
echo "Add these lines to your .env file (choose one or more):"
echo ""
echo "# SearchAPI (Recommended)"
echo "SEARCHAPI_API_KEY=your_searchapi_key_here"
echo "SEARCHAPI_ENGINE=google"
echo ""
echo "# OR Serper"
echo "SERPER_API_KEY=your_serper_key_here"
echo ""
echo "# OR Google Custom Search"
echo "GOOGLE_API_KEY=your_google_api_key_here"
echo "GOOGLE_CSE_ID=your_google_cse_id_here"
echo ""
echo "# Optional: SearXNG (if you have a SearXNG instance)"
echo "SEARXNG_ENDPOINT=http://your.searxng.server"

echo ""
echo -e "${GREEN}🎯 Next Steps:${NC}"
echo "1. Choose a search provider from the options above"
echo "2. Get the required API key(s)"
echo "3. Add to your .env file"
echo "4. Restart containers: docker compose down && docker compose up -d"
echo "5. Test search functionality"

echo ""
echo "Configuration check complete! 🎉"
