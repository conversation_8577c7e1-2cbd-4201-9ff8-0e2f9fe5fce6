#!/bin/bash

echo "🔍 Firecrawl Self-Hosted Diagnostic Check"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $2 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $1"
    else
        echo -e "${RED}✗${NC} $1"
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

echo "1. 🐳 Docker Container Status"
echo "=============================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}✗${NC} Docker is not running or not accessible"
    exit 1
fi

# Check container status
echo ""
echo "Container Status:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=firecrawl" 2>/dev/null || {
    echo "Checking all containers with 'firecrawl' in name..."
    docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -i firecrawl || echo "No containers found with 'firecrawl' in name"
}

echo ""
echo "All running containers:"
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "2. 🌐 Network Connectivity"
echo "=========================="

# Test basic connectivity to your domain
echo ""
echo "Testing connectivity to web.w-post.com..."
if curl -k -s --connect-timeout 10 "https://web.w-post.com/test" > /dev/null 2>&1; then
    print_status "HTTPS connection to web.w-post.com" 0
else
    print_status "HTTPS connection to web.w-post.com" 1
    echo "  Trying HTTP..."
    if curl -s --connect-timeout 10 "http://web.w-post.com/test" > /dev/null 2>&1; then
        print_status "HTTP connection to web.w-post.com" 0
    else
        print_status "HTTP connection to web.w-post.com" 1
    fi
fi

# Test API endpoint
echo ""
echo "Testing API endpoint..."
RESPONSE=$(curl -k -s -w "%{http_code}" -o /tmp/api_test "https://web.w-post.com/test" 2>/dev/null)
if [ "$RESPONSE" = "200" ]; then
    print_status "API endpoint responding" 0
    echo "  Response: $(cat /tmp/api_test)"
else
    print_status "API endpoint responding (HTTP $RESPONSE)" 1
fi

echo ""
echo "3. 📁 Environment Configuration"
echo "==============================="

# Check for .env file
if [ -f ".env" ]; then
    print_status ".env file exists" 0
    echo ""
    echo "Environment variables (sensitive values masked):"
    
    # Search provider configurations
    echo ""
    echo "🔍 Search Provider Configuration:"
    
    if grep -q "^GOOGLE_API_KEY=" .env; then
        if grep "^GOOGLE_API_KEY=" .env | grep -q "=.*[^=]"; then
            print_status "GOOGLE_API_KEY is set" 0
        else
            print_warning "GOOGLE_API_KEY is empty"
        fi
    else
        print_warning "GOOGLE_API_KEY not found in .env"
    fi
    
    if grep -q "^GOOGLE_CSE_ID=" .env; then
        if grep "^GOOGLE_CSE_ID=" .env | grep -q "=.*[^=]"; then
            print_status "GOOGLE_CSE_ID is set" 0
        else
            print_warning "GOOGLE_CSE_ID is empty"
        fi
    else
        print_warning "GOOGLE_CSE_ID not found in .env"
    fi
    
    if grep -q "^SEARCHAPI_API_KEY=" .env; then
        if grep "^SEARCHAPI_API_KEY=" .env | grep -q "=.*[^=]"; then
            print_status "SEARCHAPI_API_KEY is set" 0
        else
            print_warning "SEARCHAPI_API_KEY is empty"
        fi
    else
        print_warning "SEARCHAPI_API_KEY not found in .env"
    fi
    
    if grep -q "^SERPER_API_KEY=" .env; then
        if grep "^SERPER_API_KEY=" .env | grep -q "=.*[^=]"; then
            print_status "SERPER_API_KEY is set" 0
        else
            print_warning "SERPER_API_KEY is empty"
        fi
    else
        print_warning "SERPER_API_KEY not found in .env"
    fi
    
    if grep -q "^SEARXNG_ENDPOINT=" .env; then
        if grep "^SEARXNG_ENDPOINT=" .env | grep -q "=.*[^=]"; then
            print_status "SEARXNG_ENDPOINT is set" 0
        else
            print_warning "SEARXNG_ENDPOINT is empty"
        fi
    else
        print_warning "SEARXNG_ENDPOINT not found in .env"
    fi
    
    if grep -q "^FIRE_ENGINE_BETA_URL=" .env; then
        if grep "^FIRE_ENGINE_BETA_URL=" .env | grep -q "=.*[^=]"; then
            print_status "FIRE_ENGINE_BETA_URL is set" 0
        else
            print_warning "FIRE_ENGINE_BETA_URL is empty"
        fi
    else
        print_warning "FIRE_ENGINE_BETA_URL not found in .env"
    fi
    
    # Other important configurations
    echo ""
    echo "🔧 Core Configuration:"
    
    # Check authentication
    if grep -q "^USE_DB_AUTHENTICATION=" .env; then
        AUTH_VALUE=$(grep "^USE_DB_AUTHENTICATION=" .env | cut -d'=' -f2)
        if [ "$AUTH_VALUE" = "true" ]; then
            print_info "Database authentication is ENABLED"
        else
            print_info "Database authentication is DISABLED"
        fi
    else
        print_warning "USE_DB_AUTHENTICATION not set"
    fi
    
    # Check logging level
    if grep -q "^LOGGING_LEVEL=" .env; then
        LOG_LEVEL=$(grep "^LOGGING_LEVEL=" .env | cut -d'=' -f2)
        print_info "Logging level: $LOG_LEVEL"
    else
        print_info "Logging level: default (INFO)"
    fi
    
    # Check Redis configuration
    if grep -q "^REDIS_URL=" .env; then
        REDIS_URL=$(grep "^REDIS_URL=" .env | cut -d'=' -f2)
        print_info "Redis URL: $REDIS_URL"
    else
        print_info "Redis URL: default (redis://redis:6379)"
    fi
    
else
    print_status ".env file exists" 1
    echo "  Please create a .env file in the root directory"
fi

echo ""
echo "4. 🔍 Search Provider Analysis"
echo "=============================="

# Count configured search providers
SEARCH_PROVIDERS=0

if [ -f ".env" ]; then
    if grep -q "^GOOGLE_API_KEY=.*[^=]" .env && grep -q "^GOOGLE_CSE_ID=.*[^=]" .env; then
        echo "✓ Google Custom Search: Configured"
        SEARCH_PROVIDERS=$((SEARCH_PROVIDERS + 1))
    else
        echo "✗ Google Custom Search: Not fully configured"
        if grep -q "^GOOGLE_API_KEY=.*[^=]" .env; then
            echo "  - Has API key but missing CSE ID"
        fi
    fi
    
    if grep -q "^SEARCHAPI_API_KEY=.*[^=]" .env; then
        echo "✓ SearchAPI: Configured"
        SEARCH_PROVIDERS=$((SEARCH_PROVIDERS + 1))
    else
        echo "✗ SearchAPI: Not configured"
    fi
    
    if grep -q "^SERPER_API_KEY=.*[^=]" .env; then
        echo "✓ Serper: Configured"
        SEARCH_PROVIDERS=$((SEARCH_PROVIDERS + 1))
    else
        echo "✗ Serper: Not configured"
    fi
    
    if grep -q "^SEARXNG_ENDPOINT=.*[^=]" .env; then
        echo "✓ SearXNG: Configured"
        SEARCH_PROVIDERS=$((SEARCH_PROVIDERS + 1))
    else
        echo "✗ SearXNG: Not configured"
    fi
    
    if grep -q "^FIRE_ENGINE_BETA_URL=.*[^=]" .env; then
        echo "✓ Fire Engine: Configured"
        SEARCH_PROVIDERS=$((SEARCH_PROVIDERS + 1))
    else
        echo "✗ Fire Engine: Not configured"
    fi
fi

echo ""
if [ $SEARCH_PROVIDERS -eq 0 ]; then
    echo -e "${RED}⚠ No search providers configured!${NC}"
    echo "  The search will fall back to browser-based Google search"
    echo "  This may be blocked by Google's anti-bot measures"
else
    echo -e "${GREEN}✓ $SEARCH_PROVIDERS search provider(s) configured${NC}"
fi

echo ""
echo "5. 📋 Container Logs (Last 20 lines)"
echo "===================================="

# Get container logs
API_CONTAINER=$(docker ps --format "{{.Names}}" | grep -E "(api|firecrawl)" | head -1)
if [ ! -z "$API_CONTAINER" ]; then
    echo "Logs from $API_CONTAINER:"
    echo "------------------------"
    docker logs --tail 20 "$API_CONTAINER" 2>&1 | tail -20
else
    echo "No API container found"
fi

echo ""
echo "6. 🧪 API Tests"
echo "==============="

# Test basic endpoint
echo ""
echo "Testing basic endpoint..."
BASIC_TEST=$(curl -k -s -w "%{http_code}" -o /tmp/basic_test "https://web.w-post.com/" 2>/dev/null)
if [ "$BASIC_TEST" = "200" ]; then
    print_status "Basic endpoint test" 0
    echo "  Response: $(cat /tmp/basic_test)"
else
    print_status "Basic endpoint test (HTTP $BASIC_TEST)" 1
fi

# Test health endpoint
echo ""
echo "Testing health endpoint..."
HEALTH_TEST=$(curl -k -s -w "%{http_code}" -o /tmp/health_test "https://web.w-post.com/v0/health/liveness" 2>/dev/null)
if [ "$HEALTH_TEST" = "200" ]; then
    print_status "Health endpoint test" 0
else
    print_status "Health endpoint test (HTTP $HEALTH_TEST)" 1
fi

# Test search endpoint with authentication
echo ""
echo "Testing search endpoint..."
SEARCH_TEST=$(curl -k -s -w "%{http_code}" -o /tmp/search_test -X POST "https://web.w-post.com/v1/search" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer 12345" \
    -d '{"query": "test", "limit": 1}' 2>/dev/null)

echo "Search test response (HTTP $SEARCH_TEST):"
cat /tmp/search_test
echo ""

if [ "$SEARCH_TEST" = "200" ]; then
    # Check if we got actual results
    if grep -q '"data":\[\]' /tmp/search_test; then
        print_warning "Search endpoint returns empty results"
    else
        print_status "Search endpoint test" 0
    fi
else
    print_status "Search endpoint test (HTTP $SEARCH_TEST)" 1
fi

echo ""
echo "7. 📊 Summary & Recommendations"
echo "==============================="

echo ""
if [ $SEARCH_PROVIDERS -eq 0 ]; then
    echo -e "${YELLOW}🔍 SEARCH ISSUE DETECTED:${NC}"
    echo "No search providers are configured. This explains the empty search results."
    echo ""
    echo "RECOMMENDED SOLUTIONS:"
    echo "1. Add SearchAPI key (easiest):"
    echo "   - Get free key from https://searchapi.com/"
    echo "   - Add to .env: SEARCHAPI_API_KEY=your_key_here"
    echo ""
    echo "2. Complete Google Custom Search setup:"
    echo "   - Add to .env: GOOGLE_CSE_ID=your_cse_id_here"
    echo "   - Get CSE ID from https://cse.google.com/"
    echo ""
    echo "3. Add Serper key:"
    echo "   - Get key from https://serper.dev/"
    echo "   - Add to .env: SERPER_API_KEY=your_key_here"
    echo ""
    echo "After adding any search provider, restart containers:"
    echo "docker compose down && docker compose up -d"
fi

# Cleanup temp files
rm -f /tmp/api_test /tmp/basic_test /tmp/health_test /tmp/search_test

echo ""
echo "Diagnostic complete! 🎉"
