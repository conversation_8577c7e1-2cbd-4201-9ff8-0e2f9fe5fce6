# Firecrawl API Usage Guide for Agents

This comprehensive guide provides detailed instructions for agents to interact with the Firecrawl API for web scraping, crawling, and data extraction tasks.

## Base Configuration

### API Endpoints
- **Production API**: `https://api.firecrawl.dev`
- **Self-hosted**: `http://localhost:3002` (default)
- **API Version**: v1 (recommended) or v0 (legacy)

### Authentication
All API requests require authentication using Bearer tokens:

```bash
Authorization: Bearer fc-YOUR_API_KEY
```

**Note**: For self-hosted instances with `USE_DB_AUTHENTICATION=false`, authentication may be bypassed.

## Core API Endpoints

### 1. Scrape Single URL

**Endpoint**: `POST /v1/scrape`

**Purpose**: Extract content from a single webpage in LLM-ready format.

**Request Example**:
```bash
curl -X POST https://api.firecrawl.dev/v1/scrape \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "url": "https://example.com",
    "formats": ["markdown", "html", "screenshot"],
    "onlyMainContent": true,
    "includeTags": ["h1", "h2", "p"],
    "excludeTags": ["nav", "footer"],
    "waitFor": 2000,
    "timeout": 30000
  }'
```

**Response Format**:
```json
{
  "success": true,
  "data": {
    "markdown": "# Page Title\n\nContent...",
    "html": "<html>...</html>",
    "metadata": {
      "title": "Page Title",
      "description": "Page description",
      "language": "en",
      "sourceURL": "https://example.com"
    },
    "screenshot": "base64-encoded-image"
  }
}
```

### 2. Crawl Website

**Endpoint**: `POST /v1/crawl`

**Purpose**: Crawl multiple pages from a website and extract content.

**Request Example**:
```bash
curl -X POST https://api.firecrawl.dev/v1/crawl \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "url": "https://example.com",
    "limit": 100,
    "maxDepth": 3,
    "includePaths": ["/blog/*", "/docs/*"],
    "excludePaths": ["/admin/*", "/private/*"],
    "scrapeOptions": {
      "formats": ["markdown"],
      "onlyMainContent": true
    }
  }'
```

**Response Format**:
```json
{
  "success": true,
  "id": "crawl-job-id-123",
  "url": "https://api.firecrawl.dev/v1/crawl/crawl-job-id-123"
}
```

### 3. Check Crawl Status

**Endpoint**: `GET /v1/crawl/{jobId}`

**Purpose**: Monitor crawl progress and retrieve results.

**Request Example**:
```bash
curl -X GET https://api.firecrawl.dev/v1/crawl/crawl-job-id-123 \
  -H 'Authorization: Bearer fc-YOUR_API_KEY'
```

**Response Format**:
```json
{
  "success": true,
  "status": "completed",
  "completed": 45,
  "total": 45,
  "creditsUsed": 45,
  "expiresAt": "2024-07-02T10:00:00Z",
  "data": [
    {
      "markdown": "# Page 1 Content",
      "metadata": {
        "title": "Page 1",
        "sourceURL": "https://example.com/page1"
      }
    }
  ]
}
```

### 4. Batch Scrape

**Endpoint**: `POST /v1/batch/scrape`

**Purpose**: Scrape multiple URLs simultaneously.

**Request Example**:
```bash
curl -X POST https://api.firecrawl.dev/v1/batch/scrape \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "urls": [
      "https://example.com/page1",
      "https://example.com/page2",
      "https://example.com/page3"
    ],
    "formats": ["markdown"],
    "onlyMainContent": true
  }'
```

### 5. Map Website

**Endpoint**: `POST /v1/map`

**Purpose**: Get all URLs from a website quickly without scraping content.

**Request Example**:
```bash
curl -X POST https://api.firecrawl.dev/v1/map \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "url": "https://example.com",
    "search": "documentation"
  }'
```

### 6. Search Web

**Endpoint**: `POST /v1/search`

**Purpose**: Search the web and get full content from results.

**Request Example**:
```bash
curl -X POST https://api.firecrawl.dev/v1/search \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "query": "artificial intelligence news",
    "limit": 10,
    "scrapeOptions": {
      "formats": ["markdown"]
    }
  }'
```

### 7. Extract Structured Data

**Endpoint**: `POST /v1/extract`

**Purpose**: Extract structured data using AI from single or multiple pages.

**Request Example**:
```bash
curl -X POST https://api.firecrawl.dev/v1/extract \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "urls": ["https://example.com/product"],
    "schema": {
      "type": "object",
      "properties": {
        "product_name": {"type": "string"},
        "price": {"type": "number"},
        "description": {"type": "string"}
      }
    }
  }'
```

## Advanced Options

### Scrape Options
- **formats**: `["markdown", "html", "rawHtml", "screenshot", "links", "json"]`
- **onlyMainContent**: `true/false` - Extract only main content
- **includeTags**: Array of HTML tags to include
- **excludeTags**: Array of HTML tags to exclude
- **waitFor**: Milliseconds to wait before scraping
- **timeout**: Request timeout in milliseconds
- **headers**: Custom HTTP headers
- **mobile**: `true/false` - Use mobile user agent
- **proxy**: `"basic"/"stealth"/"auto"` - Proxy configuration

### Crawl Options
- **limit**: Maximum number of pages to crawl
- **maxDepth**: Maximum crawl depth
- **includePaths**: Array of path patterns to include
- **excludePaths**: Array of path patterns to exclude
- **ignoreSitemap**: `true/false` - Ignore sitemap.xml
- **allowBackwardLinks**: `true/false` - Follow backward links
- **allowExternalLinks**: `true/false` - Follow external links

## Error Handling

### Common HTTP Status Codes
- **200**: Success
- **400**: Bad Request - Invalid parameters
- **401**: Unauthorized - Invalid API key
- **402**: Payment Required - Insufficient credits
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error

### Error Response Format
```json
{
  "success": false,
  "error": "Error description",
  "details": ["Detailed error information"]
}
```

## Rate Limits

Default rate limits per minute:
- **Scrape**: 100 requests
- **Crawl**: 15 requests
- **Search**: 100 requests
- **Map**: 100 requests
- **Extract**: 100 requests

## WebSocket Support

For real-time crawl status updates:

**Endpoint**: `wss://api.firecrawl.dev/v1/crawl/{jobId}`

**Headers**: 
```
Sec-WebSocket-Protocol: Bearer fc-YOUR_API_KEY
```

## Best Practices for Agents

1. **Always check response status** before processing data
2. **Implement retry logic** with exponential backoff for rate limits
3. **Use appropriate formats** based on your needs (markdown for LLMs, html for parsing)
4. **Set reasonable timeouts** to avoid hanging requests
5. **Monitor credit usage** through `/v1/team/credit-usage` endpoint
6. **Use batch operations** for multiple URLs when possible
7. **Implement proper error handling** for all API calls

## Self-Hosted Configuration

For self-hosted instances, update the base URL:
```bash
# Instead of https://api.firecrawl.dev
curl -X POST http://your-domain.com:3002/v1/scrape
```

## Health Checks

Monitor API health:
- **Liveness**: `GET /v0/health/liveness`
- **Readiness**: `GET /v0/health/readiness`

## SDK Integration Examples

### Python SDK
```python
from firecrawl import FirecrawlApp

# Initialize the app
app = FirecrawlApp(api_key="fc-YOUR_API_KEY")

# Scrape a single URL
result = app.scrape_url(
    "https://example.com",
    formats=["markdown", "html"],
    only_main_content=True
)

# Crawl a website
crawl_result = app.crawl_url(
    "https://example.com",
    limit=50,
    scrape_options={
        "formats": ["markdown"],
        "only_main_content": True
    }
)

# Search the web
search_result = app.search(
    "AI news",
    limit=10
)
```

### JavaScript/Node.js SDK
```javascript
import FirecrawlApp from '@mendable/firecrawl-js';

const app = new FirecrawlApp({apiKey: "fc-YOUR_API_KEY"});

// Scrape a URL
const scrapeResult = await app.scrapeUrl("https://example.com", {
  formats: ["markdown", "html"],
  onlyMainContent: true
});

// Crawl a website
const crawlResult = await app.crawlUrl("https://example.com", {
  limit: 50,
  scrapeOptions: {
    formats: ["markdown"]
  }
});
```

## Advanced Use Cases

### 1. E-commerce Product Extraction
```bash
curl -X POST https://api.firecrawl.dev/v1/extract \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "urls": ["https://store.example.com/product/123"],
    "schema": {
      "type": "object",
      "properties": {
        "name": {"type": "string"},
        "price": {"type": "number"},
        "currency": {"type": "string"},
        "availability": {"type": "string"},
        "rating": {"type": "number"},
        "reviews_count": {"type": "integer"},
        "description": {"type": "string"},
        "images": {
          "type": "array",
          "items": {"type": "string"}
        },
        "specifications": {
          "type": "object",
          "additionalProperties": {"type": "string"}
        }
      }
    }
  }'
```

### 2. News Article Monitoring
```bash
curl -X POST https://api.firecrawl.dev/v1/crawl \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "url": "https://news-site.com",
    "includePaths": ["/articles/*", "/news/*"],
    "excludePaths": ["/ads/*", "/sponsored/*"],
    "limit": 200,
    "scrapeOptions": {
      "formats": ["markdown"],
      "onlyMainContent": true,
      "includeTags": ["h1", "h2", "p", "time"],
      "excludeTags": ["nav", "footer", "aside", "advertisement"]
    }
  }'
```

### 3. Documentation Scraping
```bash
curl -X POST https://api.firecrawl.dev/v1/crawl \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "url": "https://docs.example.com",
    "includePaths": ["/docs/*", "/api/*", "/guides/*"],
    "maxDepth": 5,
    "scrapeOptions": {
      "formats": ["markdown", "html"],
      "onlyMainContent": true,
      "waitFor": 1000
    }
  }'
```

### 4. Real Estate Listings
```bash
curl -X POST https://api.firecrawl.dev/v1/extract \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer fc-YOUR_API_KEY' \
  -d '{
    "urls": ["https://realestate.com/listing/123"],
    "schema": {
      "type": "object",
      "properties": {
        "address": {"type": "string"},
        "price": {"type": "number"},
        "bedrooms": {"type": "integer"},
        "bathrooms": {"type": "number"},
        "square_feet": {"type": "integer"},
        "property_type": {"type": "string"},
        "listing_date": {"type": "string"},
        "agent_name": {"type": "string"},
        "agent_phone": {"type": "string"},
        "description": {"type": "string"},
        "amenities": {
          "type": "array",
          "items": {"type": "string"}
        }
      }
    }
  }'
```

## Monitoring and Analytics

### Credit Usage Tracking
```bash
curl -X GET https://api.firecrawl.dev/v1/team/credit-usage \
  -H 'Authorization: Bearer fc-YOUR_API_KEY'
```

### Token Usage (for Extract operations)
```bash
curl -X GET https://api.firecrawl.dev/v1/team/token-usage \
  -H 'Authorization: Bearer fc-YOUR_API_KEY'
```

### Active Crawls Monitoring
```bash
curl -X GET https://api.firecrawl.dev/v1/crawl/active \
  -H 'Authorization: Bearer fc-YOUR_API_KEY'
```

## Troubleshooting

### Common Issues and Solutions

1. **Rate Limit Exceeded (429)**
   - Implement exponential backoff
   - Reduce request frequency
   - Consider upgrading plan

2. **Timeout Errors**
   - Increase timeout values
   - Use smaller batch sizes
   - Check target website performance

3. **Authentication Errors (401)**
   - Verify API key format: `fc-YOUR_API_KEY`
   - Check API key validity
   - Ensure proper Bearer token format

4. **Content Not Found**
   - Adjust `waitFor` parameter
   - Check if content is JavaScript-rendered
   - Verify URL accessibility

5. **Blocked by Anti-Bot Systems**
   - Use `proxy: "stealth"` option
   - Add realistic headers
   - Implement delays between requests

### Debug Mode
For self-hosted instances, enable debug logging:
```bash
LOGGING_LEVEL=DEBUG
```

## Performance Optimization

### Batch Processing
- Use `/v1/batch/scrape` for multiple URLs
- Implement parallel processing with rate limiting
- Group similar requests together

### Efficient Crawling
- Use `map` endpoint first to discover URLs
- Set appropriate `limit` and `maxDepth`
- Use `includePaths` and `excludePaths` effectively

### Memory Management
- Process large crawls in chunks
- Use streaming for real-time data
- Implement proper cleanup for completed jobs

## Security Considerations

1. **API Key Protection**
   - Store API keys securely
   - Use environment variables
   - Rotate keys regularly

2. **Data Privacy**
   - Be aware of scraped content sensitivity
   - Implement proper data retention policies
   - Follow GDPR/privacy regulations

3. **Rate Limiting**
   - Respect target website's robots.txt
   - Implement proper delays
   - Monitor for IP blocking

This comprehensive guide provides everything agents need to effectively integrate with the Firecrawl API. For the latest updates and additional features, refer to the official documentation at https://docs.firecrawl.dev.
