import axios from "axios";
import dotenv from "dotenv";
import { SearchResult } from "../../src/lib/entities";

dotenv.config();

interface GoogleCustomSearchOptions {
  tbs?: string;
  filter?: string;
  lang?: string;
  country?: string;
  location?: string;
  num_results: number;
  page?: number;
}

export async function googleCustomSearch(
  q: string,
  options: GoogleCustomSearchOptions,
): Promise<SearchResult[]> {
  const apiKey = process.env.GOOGLE_API_KEY;
  const cseId = process.env.GOOGLE_CSE_ID;

  if (!apiKey || !cseId) {
    console.warn("Google Custom Search API key or CSE ID not configured");
    return [];
  }

  const params = {
    key: apiKey,
    cx: cseId,
    q: q,
    num: Math.min(options.num_results, 10), // Google CSE max is 10 per request
    start: ((options.page ?? 1) - 1) * 10 + 1,
    hl: options.lang || "en",
    gl: options.country || "us",
    safe: "active",
  };

  const url = "https://www.googleapis.com/customsearch/v1";

  try {
    const response = await axios.get(url, {
      params: params,
      timeout: 10000,
    });

    if (response.status !== 200) {
      throw new Error(`Google Custom Search API returned status ${response.status}`);
    }

    const data = response.data;

    if (data && Array.isArray(data.items)) {
      return data.items.map((item: any) => ({
        url: item.link,
        title: item.title,
        description: item.snippet || "",
      }));
    } else {
      return [];
    }
  } catch (error) {
    console.error(`Error with Google Custom Search API: ${error.message}`);
    return [];
  }
}
